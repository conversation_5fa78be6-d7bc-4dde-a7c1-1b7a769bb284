from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import os
from dotenv import load_dotenv

from rag_chain import create_rag_chain_from_wikipedia, create_rag_chain_from_session_documents
from chat_history import ChatHistoryManager
from config import config
from app_state import app_state

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="LangChain RAG API", version="1.0.0")

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# Initialize components on startup
@app.on_event("startup")
async def startup_event():
    await app_state.on_startup()
    
    # For backward compatibility with the rest of the code
    global rag_chain, chat_history_manager
    rag_chain = app_state.get_rag_chain()
    chat_history_manager = app_state.get_chat_history_manager()

# Request/Response models
class ChatRequest(BaseModel):
    session_id: str
    message: str

class ChatResponse(BaseModel):
    session_id: str
    response: str
    sources: Optional[List[str]] = None

class ChatHistoryResponse(BaseModel):
    session_id: str
    history: List[dict]

class DocumentResponse(BaseModel):
    id: int
    filename: str
    upload_timestamp: str

class DocumentListResponse(BaseModel):
    session_id: str
    documents: List[DocumentResponse]

# API endpoints
@app.get("/")
async def root():
    """Serve the frontend HTML file"""
    return FileResponse("frontend/index.html")

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Handle chat messages with RAG functionality"""
    global rag_chain, chat_history_manager
    
    # Get chat history for this session
    chat_history = chat_history_manager.get_history(request.session_id)
    
    # Check if session has documents
    session_documents = chat_history_manager.get_documents(request.session_id)
    
    # Create appropriate RAG chain
    if session_documents:
        # Use session-specific documents
        session_docs_list = [
            {"content": doc["content"], "filename": doc["filename"]} 
            for doc in session_documents
        ]
        session_rag_chain = create_rag_chain_from_session_documents(session_docs_list)
        
        if session_rag_chain:
            # Use session-specific RAG chain
            input_data = {
                "question": request.message,
                "chat_history": chat_history
            }
            result = await session_rag_chain.ainvoke(input_data)
        else:
            # Fallback to default RAG chain if session chain creation fails
            input_data = {
                "question": request.message,
                "chat_history": chat_history
            }
            result = await rag_chain.ainvoke(input_data)
    else:
        # Use default RAG chain (Wikipedia)
        input_data = {
            "question": request.message,
            "chat_history": chat_history
        }
        result = await rag_chain.ainvoke(input_data)
    
    # Save the new interaction to history
    chat_history_manager.add_to_history(
        request.session_id, 
        request.message, 
        result
    )
    
    return ChatResponse(
        session_id=request.session_id,
        response=result
    )

@app.get("/history/{session_id}", response_model=ChatHistoryResponse)
async def get_history(session_id: str):
    """Get chat history for a session"""
    global chat_history_manager
    history = chat_history_manager.get_history(session_id)
    return ChatHistoryResponse(session_id=session_id, history=[{"role": msg[0], "content": msg[1]} for msg in history])

@app.delete("/history/{session_id}")
async def clear_history(session_id: str):
    """Clear chat history for a session"""
    global chat_history_manager
    chat_history_manager.clear_history(session_id)
    return {"message": f"History cleared for session {session_id}"}

@app.post("/upload/{session_id}")
async def upload_document(session_id: str, file: UploadFile = File(...)):
    """Upload a document for a session"""
    global chat_history_manager
    
    try:
        # Read the file content
        content = await file.read()
        text_content = content.decode('utf-8')
        
        # Save document to database
        doc_id = chat_history_manager.add_document(session_id, file.filename, text_content)
        
        if doc_id:
            return JSONResponse(
                status_code=200,
                content={"message": "Document uploaded successfully", "document_id": doc_id}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"message": "Failed to save document"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"Error uploading document: {str(e)}"}
        )

@app.get("/documents/{session_id}", response_model=DocumentListResponse)
async def list_documents(session_id: str):
    """List all documents for a session"""
    global chat_history_manager
    
    try:
        documents = chat_history_manager.get_documents(session_id)
        
        # Convert to the response format
        doc_list = [
            DocumentResponse(
                id=doc['id'],
                filename=doc['filename'],
                upload_timestamp=doc['upload_timestamp'].isoformat() if hasattr(doc['upload_timestamp'], 'isoformat') else str(doc['upload_timestamp'])
            )
            for doc in documents
        ]
        
        return DocumentListResponse(session_id=session_id, documents=doc_list)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"Error listing documents: {str(e)}"}
        )

@app.delete("/documents/{session_id}/{document_id}")
async def delete_document(session_id: str, document_id: int):
    """Delete a specific document from a session"""
    global chat_history_manager
    
    try:
        success = chat_history_manager.delete_document(session_id, document_id)
        
        if success:
            return {"message": "Document deleted successfully"}
        else:
            return JSONResponse(
                status_code=404,
                content={"message": "Document not found"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"Error deleting document: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)