from sqlalchemy.orm import Session
from typing import List, Tuple, Optional
from sqlalchemy.exc import SQLAlchemyError
from models import ChatHistory
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ChatHistoryRepository:
    """Repository for managing chat history data access"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def add_to_history(self, session_id: str, user_message: str, ai_response: str) -> bool:
        """
        Add user message and AI response to chat history
        
        Args:
            session_id: The session identifier
            user_message: The user's message
            ai_response: The AI's response
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Add user message
            user_entry = ChatHistory(
                session_id=session_id,
                message_type='user',
                content=user_message
            )
            self.db.add(user_entry)
            
            # Add AI response
            ai_entry = ChatHistory(
                session_id=session_id,
                message_type='ai',
                content=ai_response
            )
            self.db.add(ai_entry)
            
            self.db.commit()
            logger.info(f"Added chat history for session {session_id}")
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error adding to chat history: {e}")
            return False
    
    def get_history(self, session_id: str) -> List[Tuple[str, str]]:
        """
        Get chat history for a session as a list of (role, content) tuples
        
        Args:
            session_id: The session identifier
            
        Returns:
            List[Tuple[str, str]]: List of (role, content) tuples
        """
        try:
            entries = self.db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).order_by(ChatHistory.timestamp.asc()).all()
            
            # Convert to the format expected by our application
            history = []
            for entry in entries:
                if entry.message_type == 'user':
                    history.append(('user', entry.content))
                elif entry.message_type == 'ai':
                    history.append(('assistant', entry.content))
            
            logger.debug(f"Retrieved {len(history)} history entries for session {session_id}")
            return history
        except SQLAlchemyError as e:
            logger.error(f"Error getting chat history: {e}")
            return []
    
    def clear_history(self, session_id: str) -> int:
        """
        Clear chat history for a session
        
        Args:
            session_id: The session identifier
            
        Returns:
            int: Number of records deleted
        """
        try:
            count = self.db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).delete()
            self.db.commit()
            logger.info(f"Cleared {count} history entries for session {session_id}")
            return count
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error clearing chat history: {e}")
            return 0