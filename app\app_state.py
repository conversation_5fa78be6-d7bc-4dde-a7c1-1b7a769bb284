from typing import Op<PERSON>
from config import config
from chat_history import ChatHistoryManager
from rag_chain import create_rag_chain_from_wikipedia

class AppState:
    def __init__(self):
        self.rag_chain: Optional[object] = None
        self.chat_history_manager: Optional[ChatHistoryManager] = None
    
    def initialize_components(self):
        """Initialize all application components"""
        self.rag_chain = create_rag_chain_from_wikipedia()
        self.chat_history_manager = ChatHistoryManager()
    
    async def on_startup(self):
        """Startup event handler"""
        self.initialize_components()
    
    def get_rag_chain(self):
        return self.rag_chain
    
    def get_chat_history_manager(self):
        return self.chat_history_manager

# Global app state instance
app_state = AppState()