# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Retrieval Augmented Generation (RAG) application built with LangChain that uses OpenRouter as the LLM provider and includes chat session persistence. The application features a web-based chat interface with session management and PostgreSQL database for storing conversation history.

## Code Architecture and Structure

### Backend (app/)
- `main.py`: FastAPI application with API endpoints for chat, history retrieval, and history clearing
- `rag_chain.py`: Implementation of the RAG chain with document retrieval, OpenRouter integration, and chat history support
- `chat_history.py`: PostgreSQL integration for managing chat history with session-based conversations
- `requirements.txt`: Python dependencies including LangChain, FastAPI, and PostgreSQL drivers

### Frontend (app/frontend/)
- `index.html`: Simple chat interface with JavaScript for API interactions

### Infrastructure
- `Dockerfile`: Docker configuration for the application container
- `docker-compose.yml`: Multi-container setup with application and PostgreSQL database
- `.env.example`: Example environment configuration file

### Key Components
1. **RAG Pipeline**: Document loading from web sources, text chunking, embedding with OpenAI embeddings, and vector storage with FAISS
2. **LLM Integration**: OpenRouter API integration using the z-ai/glm-4.5-air:free model
3. **Session Persistence**: PostgreSQL database for chat history with session-based conversation management
4. **Web Interface**: Real-time chat interface with session management
5. **Containerization**: Docker containerization for easy deployment

## Common Development Tasks

### Running the Application
1. Copy `.env.example` to `.env` and add your OpenRouter API key
2. Run `docker-compose up --build`
3. Access the application at `http://localhost:8000`

### Development Workflow
1. Modify Python files in the `app` directory
2. Rebuild and restart containers with:
   ```bash
   docker-compose down
   docker-compose up --build
   ```
### Doing Online Research
1. Use Jina ONLY - NOT built in web search tool - it DOESN'T work here.
2. General search: curl "https://s.jina.ai/?q=Jina+AI" \
  -H "Authorization: Bearer jina_fb1ba4e5cdd346d79d9fb60520179a61PZ5lzYjU2x_J9-vYE70Vh8Bugrbz" \
  -H "X-Respond-With: no-content"
3. Fetch specific URL: curl "https://r.jina.ai/https://www.example.com" \
  -H "Authorization: Bearer jina_fb1ba4e5cdd346d79d9fb60520179a61PZ5lzYjU2x_J9-vYE70Vh8Bugrbz"


### Local Development (without Docker)
1. Install dependencies: `pip install -r requirements.txt`
2. Run the application: `python main.py`

## Testing

### API Testing
- `POST /chat`: Send a message and get a response
- `GET /history/{session_id}`: Retrieve chat history
- `DELETE /history/{session_id}`: Clear chat history

Manual testing can be performed using curl or Postman as documented in the research/testing_the_application.md file.

## Customization Points

1. **Changing the LLM Model**: Edit `rag_chain.py` and modify the `model` parameter in the ChatOpenAI initialization
2. **Adding More Documents**: Modify the `create_rag_chain()` function in `rag_chain.py` to load documents from different sources
3. **Changing the Prompt**: Edit the template in `rag_chain.py` to modify how the LLM is prompted
4. **Database Configuration**: Modify connection parameters in `chat_history.py` and `docker-compose.yml`

## Technologies Used
- Backend: Python, FastAPI, LangChain
- Frontend: HTML, CSS, JavaScript
- Database: PostgreSQL
- Vector Store: FAISS
- LLM Provider: OpenRouter
- Containerization: Docker, Docker Compose