from sqlalchemy.orm import Session
from typing import List, Dict, Any
from sqlalchemy.exc import SQLAlchemyError
from models import SessionDocuments
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class DocumentRepository:
    """Repository for managing document data access"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def add_document(self, session_id: str, filename: str, content: str) -> Optional[int]:
        """
        Add a document to a session
        
        Args:
            session_id: The session identifier
            filename: The document filename
            content: The document content
            
        Returns:
            Optional[int]: The document ID if successful, None otherwise
        """
        try:
            document = SessionDocuments(
                session_id=session_id,
                filename=filename,
                content=content
            )
            self.db.add(document)
            self.db.commit()
            self.db.refresh(document)
            logger.info(f"Added document {filename} for session {session_id}")
            return document.id
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error adding document: {e}")
            return None
    
    def get_documents(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get all documents for a session
        
        Args:
            session_id: The session identifier
            
        Returns:
            List[Dict[str, Any]]: List of document dictionaries
        """
        try:
            documents = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id
            ).order_by(SessionDocuments.upload_timestamp.asc()).all()
            
            result = [doc.to_dict() for doc in documents]
            logger.debug(f"Retrieved {len(result)} documents for session {session_id}")
            return result
        except SQLAlchemyError as e:
            logger.error(f"Error getting documents: {e}")
            return []
    
    def delete_document(self, session_id: str, document_id: int) -> bool:
        """
        Delete a specific document from a session
        
        Args:
            session_id: The session identifier
            document_id: The document ID to delete
            
        Returns:
            bool: True if document was deleted, False otherwise
        """
        try:
            count = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id,
                SessionDocuments.id == document_id
            ).delete()
            self.db.commit()
            if count > 0:
                logger.info(f"Deleted document {document_id} for session {session_id}")
            return count > 0
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error deleting document: {e}")
            return False
    
    def clear_documents(self, session_id: str) -> int:
        """
        Clear all documents for a session
        
        Args:
            session_id: The session identifier
            
        Returns:
            int: Number of documents deleted
        """
        try:
            count = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id
            ).delete()
            self.db.commit()
            logger.info(f"Cleared {count} documents for session {session_id}")
            return count
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Error clearing documents: {e}")
            return 0