import psycopg2
from psycopg2.extras import RealDict<PERSON><PERSON><PERSON>
from typing import List, <PERSON><PERSON>
from datetime import datetime
from config import config

class ChatHistoryManager:
    """Manage chat history with PostgreSQL database"""
    
    def __init__(self):
        # Database connection parameters
        self.db_params = {
            'host': config.DB_HOST,
            'database': config.POSTGRES_DB,
            'user': config.POSTGRES_USER,
            'password': config.POSTGRES_PASSWORD,
            'port': config.DB_PORT
        }
        
        # Create tables if they don't exist
        self._initialize_database()
    
    def _get_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_params)
    
    def _initialize_database(self):
        """Create chat_history and session_documents tables if they don't exist"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Create chat_history table
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS chat_history (
                            id SERIAL PRIMARY KEY,
                            session_id VARCHAR(255) NOT NULL,
                            message_type VARCHAR(10) NOT NULL,
                            content TEXT NOT NULL,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    
                    # Create session_documents table for document upload feature
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS session_documents (
                            id SERIAL PRIMARY KEY,
                            session_id VARCHAR(255) NOT NULL,
                            filename VARCHAR(255) NOT NULL,
                            content TEXT NOT NULL,
                            upload_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    conn.commit()
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def add_to_history(self, session_id: str, user_message: str, ai_response: str):
        """Add user message and AI response to chat history"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Add user message
                    cur.execute("""
                        INSERT INTO chat_history (session_id, message_type, content)
                        VALUES (%s, %s, %s)
                    """, (session_id, 'user', user_message))
                    
                    # Add AI response
                    cur.execute("""
                        INSERT INTO chat_history (session_id, message_type, content)
                        VALUES (%s, %s, %s)
                    """, (session_id, 'ai', ai_response))
                    
                    conn.commit()
        except Exception as e:
            print(f"Error adding to history: {e}")
    
    def get_history(self, session_id: str) -> List[Tuple[str, str]]:
        """Get chat history for a session as a list of (role, content) tuples"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("""
                        SELECT message_type, content, timestamp
                        FROM chat_history
                        WHERE session_id = %s
                        ORDER BY timestamp ASC
                    """, (session_id,))
                    
                    rows = cur.fetchall()
                    # Convert to the format expected by our application
                    history = []
                    for row in rows:
                        if row['message_type'] == 'user':
                            history.append(('user', row['content']))
                        elif row['message_type'] == 'ai':
                            history.append(('assistant', row['content']))
                    
                    return history
        except Exception as e:
            print(f"Error getting history: {e}")
            return []
    
    def clear_history(self, session_id: str):
        """Clear chat history for a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        DELETE FROM chat_history
                        WHERE session_id = %s
                    """, (session_id,))
                    conn.commit()
        except Exception as e:
            print(f"Error clearing history: {e}")
    
    def add_document(self, session_id: str, filename: str, content: str):
        """Add a document to a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        INSERT INTO session_documents (session_id, filename, content)
                        VALUES (%s, %s, %s)
                    """, (session_id, filename, content))
                    conn.commit()
                    return cur.lastrowid
        except Exception as e:
            print(f"Error adding document: {e}")
            return None
    
    def get_documents(self, session_id: str):
        """Get all documents for a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("""
                        SELECT id, filename, content, upload_timestamp
                        FROM session_documents
                        WHERE session_id = %s
                        ORDER BY upload_timestamp ASC
                    """, (session_id,))
                    return cur.fetchall()
        except Exception as e:
            print(f"Error getting documents: {e}")
            return []
    
    def delete_document(self, session_id: str, document_id: int):
        """Delete a specific document from a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        DELETE FROM session_documents
                        WHERE session_id = %s AND id = %s
                    """, (session_id, document_id))
                    conn.commit()
                    return cur.rowcount > 0
        except Exception as e:
            print(f"Error deleting document: {e}")
            return False
    
    def clear_documents(self, session_id: str):
        """Clear all documents for a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        DELETE FROM session_documents
                        WHERE session_id = %s
                    """, (session_id,))
                    conn.commit()
                    return cur.rowcount
        except Exception as e:
            print(f"Error clearing documents: {e}")
            return 0