<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lang<PERSON><PERSON>n <PERSON>G Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-history {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: 20%;
        }
        .ai-message {
            background-color: #f5f5f5;
            margin-right: 20%;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        #user-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #bbdefb;
            cursor: not-allowed;
        }
        .session-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        #session-id {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .document-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .document-list {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        .document-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .document-item:last-child {
            border-bottom: none;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #2196f3;
        }
        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>LangChain RAG Chat</h1>
        
        <div class="session-controls">
            <label for="session-id">Session ID:</label>
            <input type="text" id="session-id" placeholder="Enter session ID" value="session1">
            <button onclick="clearHistory()">Clear History</button>
        </div>
        
        <div class="document-section">
            <h3>Document Management</h3>
            <div class="upload-area" id="upload-area" onclick="document.getElementById('file-input').click()">
                <p>Click to upload documents or drag and drop files here</p>
                <p><small>Supported formats: .txt, .pdf, .docx</small></p>
                <input type="file" id="file-input" style="display: none;" multiple onchange="uploadDocuments()">
            </div>
            
            <div class="document-list" id="document-list">
                <p>No documents uploaded for this session.</p>
            </div>
            <button onclick="loadDocuments()">Refresh Document List</button>
        </div>
        
        <div class="chat-history" id="chat-history"></div>
        
        <div class="input-area">
            <input type="text" id="user-input" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="send-button">Send</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Set up drag and drop functionality
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('upload-area');
            
            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });
            
            // Highlight drop area when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });
            
            // Handle dropped files
            uploadArea.addEventListener('drop', handleDrop, false);
            
            // Load chat history and documents on page load
            loadChatHistory();
            loadDocuments();
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        function highlight() {
            document.getElementById('upload-area').classList.add('dragover');
        }
        
        function unhighlight() {
            document.getElementById('upload-area').classList.remove('dragover');
        }
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        function handleFiles(files) {
            ([...files]).forEach(uploadFile);
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const userInput = document.getElementById('user-input');
            const message = userInput.value.trim();
            const sessionId = document.getElementById('session-id').value;
            
            if (!message) return;
            
            const sendButton = document.getElementById('send-button');
            sendButton.disabled = true;
            userInput.disabled = true;
            
            // Add user message to chat
            addMessageToChat('user', message);
            userInput.value = '';
            
            try {
                // Send message to backend
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        message: message
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Add AI response to chat
                addMessageToChat('ai', data.response);
            } catch (error) {
                console.error('Error:', error);
                addMessageToChat('ai', 'Sorry, I encountered an error. Please try again.');
            } finally {
                sendButton.disabled = false;
                userInput.disabled = false;
                userInput.focus();
            }
        }
        
        async function clearHistory() {
            const sessionId = document.getElementById('session-id').value;
            
            try {
                const response = await fetch(`${API_BASE}/history/${sessionId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Clear chat display
                document.getElementById('chat-history').innerHTML = '';
            } catch (error) {
                console.error('Error clearing history:', error);
                alert('Error clearing history');
            }
        }
        
        function addMessageToChat(role, content) {
            const chatHistory = document.getElementById('chat-history');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.textContent = content;
            chatHistory.appendChild(messageDiv);
            
            // Scroll to bottom
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        async function loadChatHistory() {
            const sessionId = document.getElementById('session-id').value;
            
            try {
                const response = await fetch(`${API_BASE}/history/${sessionId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Clear current chat display
                const chatHistory = document.getElementById('chat-history');
                chatHistory.innerHTML = '';
                
                // Add history messages to chat
                data.history.forEach(msg => {
                    addMessageToChat(msg.role, msg.content);
                });
            } catch (error) {
                console.error('Error loading history:', error);
            }
        }
        
        async function uploadDocuments() {
            const files = document.getElementById('file-input').files;
            const sessionId = document.getElementById('session-id').value;
            
            if (files.length === 0) {
                alert('Please select at least one file to upload.');
                return;
            }
            
            const uploadPromises = [];
            for (let i = 0; i < files.length; i++) {
                uploadPromises.push(uploadFile(files[i], sessionId));
            }
            
            try {
                await Promise.all(uploadPromises);
                alert('All files uploaded successfully!');
                document.getElementById('file-input').value = ''; // Clear file input
                loadDocuments(); // Refresh document list
            } catch (error) {
                console.error('Error uploading files:', error);
                alert('Error uploading files. Please try again.');
            }
        }
        
        async function uploadFile(file, sessionId) {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch(`${API_BASE}/upload/${sessionId}`, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`Failed to upload ${file.name}`);
            }
            
            return response.json();
        }
        
        async function loadDocuments() {
            const sessionId = document.getElementById('session-id').value;
            const documentList = document.getElementById('document-list');
            
            try {
                const response = await fetch(`${API_BASE}/documents/${sessionId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.documents.length === 0) {
                    documentList.innerHTML = '<p>No documents uploaded for this session.</p>';
                } else {
                    documentList.innerHTML = '';
                    data.documents.forEach(doc => {
                        const docElement = document.createElement('div');
                        docElement.className = 'document-item';
                        docElement.innerHTML = `
                            <span>${doc.filename}</span>
                            <button onclick="deleteDocument(${doc.id})">Delete</button>
                        `;
                        documentList.appendChild(docElement);
                    });
                }
            } catch (error) {
                console.error('Error loading documents:', error);
                documentList.innerHTML = '<p>Error loading documents.</p>';
            }
        }
        
        async function deleteDocument(documentId) {
            const sessionId = document.getElementById('session-id').value;
            
            if (!confirm('Are you sure you want to delete this document?')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/documents/${sessionId}/${documentId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                loadDocuments(); // Refresh document list
            } catch (error) {
                console.error('Error deleting document:', error);
                alert('Error deleting document');
            }
        }
    </script>
</body>
</html>