import os
import sys
import unittest
from unittest.mock import patch

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

class TestConfig(unittest.TestCase):
    
    def setUp(self):
        # Clear the config instance before each test
        import app.config
        app.config._config_instance = None
    
    @patch.dict(os.environ, {
        "OPENROUTER_API_KEY": "test_key_123",
        "DB_HOST": "test_host",
        "POSTGRES_DB": "test_db",
        "POSTGRES_USER": "test_user",
        "POSTGRES_PASSWORD": "test_password",
        "DB_PORT": "5433",
        "OLLAMA_BASE_URL": "http://test-ollama:11434"
    })
    def test_config_with_all_env_vars(self):
        from app.config import Config
        config = Config()
        
        # Test all configuration values
        self.assertEqual(config.OPENROUTER_API_KEY, "test_key_123")
        self.assertEqual(config.DB_HOST, "test_host")
        self.assertEqual(config.POSTGRES_DB, "test_db")
        self.assertEqual(config.POSTGRES_USER, "test_user")
        self.assertEqual(config.POSTGRES_PASSWORD, "test_password")
        self.assertEqual(config.DB_PORT, "5433")
        self.assertEqual(config.OLLAMA_BASE_URL, "http://test-ollama:11434")
    
    @patch.dict(os.environ, {
        "OPENROUTER_API_KEY": "test_key_123"
    })
    def test_config_with_defaults(self):
        from app.config import Config
        config = Config()
        
        # Test required configuration
        self.assertEqual(config.OPENROUTER_API_KEY, "test_key_123")
        
        # Test default values
        self.assertEqual(config.DB_HOST, "db")
        self.assertEqual(config.POSTGRES_DB, "langchain_rag")
        self.assertEqual(config.POSTGRES_USER, "langchain")
        self.assertEqual(config.POSTGRES_PASSWORD, "langchain123")
        self.assertEqual(config.DB_PORT, "5432")
        self.assertEqual(config.OLLAMA_BASE_URL, "http://localhost:11434")
    
    @patch.dict(os.environ, {}, clear=True)
    def test_config_missing_required_key(self):
        from app.config import Config
        
        # This should raise a ValueError
        with self.assertRaises(ValueError) as context:
            Config()
        
        self.assertIn("Required environment variable OPENROUTER_API_KEY is not set", str(context.exception))

if __name__ == '__main__':
    unittest.main()