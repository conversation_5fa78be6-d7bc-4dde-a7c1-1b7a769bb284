from langchain_core.documents import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings
from typing import List, Dict, Any
import os
from config import config

def create_documents_from_texts(texts: List[str], filenames: List[str] = None) -> List[Document]:
    """
    Create LangChain Document objects from text content.
    
    Args:
        texts: List of text content strings
        filenames: Optional list of filenames for metadata (if not provided, generic names will be used)
    
    Returns:
        List of Document objects
    """
    documents = []
    
    for i, text in enumerate(texts):
        filename = filenames[i] if filenames and i < len(filenames) else f"document_{i}"
        
        doc = Document(
            page_content=text,
            metadata={
                "source": filename,
                "doc_id": i
            }
        )
        documents.append(doc)
    
    return documents

def process_documents_for_rag(documents: List[Document]) -> List[Document]:
    """
    Process documents for RAG by splitting them into chunks.
    
    Args:
        documents: List of Document objects
    
    Returns:
        List of split Document objects
    """
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200
    )
    splits = text_splitter.split_documents(documents)
    
    return splits

def create_vectorstore_from_documents(documents: List[Document]) -> Any:
    """
    Create a FAISS vectorstore from documents.
    
    Args:
        documents: List of Document objects (should be pre-processed/split)
    
    Returns:
        FAISS vectorstore object
    """
    # Create embeddings and vector store
    embeddings = OllamaEmbeddings(
        model="nomic-embed-text",
        base_url=config.OLLAMA_BASE_URL
    )
    vectorstore = FAISS.from_documents(documents, embeddings)
    
    return vectorstore

def create_rag_chain_from_documents(documents: List[Document]):
    """
    Create a RAG chain from a list of documents.
    
    Args:
        documents: List of Document objects
    
    Returns:
        RAG chain retriever
    """
    # Process documents (splitting)
    processed_docs = process_documents_for_rag(documents)
    
    # Create vectorstore
    vectorstore = create_vectorstore_from_documents(processed_docs)
    
    # Return retriever
    return vectorstore.as_retriever()