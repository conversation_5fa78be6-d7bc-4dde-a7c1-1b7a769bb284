# Phase 1 Implementation: Configuration and Dependency Management

## Step 1: Create config.py

This module will centralize all configuration management, including:
- Loading environment variables
- Validating required configuration values
- Providing type-safe access to configuration values
- Setting appropriate default values

## Step 2: Create app_state.py

This module will:
- Create an application state container
- Implement dependency injection setup
- Manage lifecycle of components (RAG chain, database connections, etc.)

## Implementation Details

### config.py
```python
import os
from typing import Optional

class Config:
    def __init__(self):
        # Load and validate OpenRouter configuration
        self.OPENROUTER_API_KEY: str = self._get_required_env_var("OPENROUTER_API_KEY")
        
        # Database configuration
        self.DB_HOST: str = os.getenv("DB_HOST", "db")
        self.POSTGRES_DB: str = os.getenv("POSTGRES_DB", "langchain_rag")
        self.POSTGRES_USER: str = os.getenv("POSTGRES_USER", "langchain")
        self.POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "langchain123")
        self.DB_PORT: str = os.getenv("DB_PORT", "5432")
        
        # Ollama configuration
        self.OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        
    def _get_required_env_var(self, var_name: str) -> str:
        value = os.getenv(var_name)
        if not value:
            raise ValueError(f"Required environment variable {var_name} is not set")
        return value

# Global config instance
config = Config()
```

### app_state.py
```python
from typing import Optional
from fastapi import FastAPI
from config import config
from chat_history import ChatHistoryManager
from rag_chain import create_rag_chain_from_wikipedia

class AppState:
    def __init__(self):
        self.rag_chain: Optional[object] = None
        self.chat_history_manager: Optional[ChatHistoryManager] = None
    
    def initialize_components(self):
        """Initialize all application components"""
        self.rag_chain = create_rag_chain_from_wikipedia()
        self.chat_history_manager = ChatHistoryManager()
    
    async def on_startup(self):
        """Startup event handler"""
        self.initialize_components()
    
    def get_rag_chain(self):
        return self.rag_chain
    
    def get_chat_history_manager(self):
        return self.chat_history_manager

# Global app state instance
app_state = AppState()
```

### Updated main.py (partial)
```python
from fastapi import FastAPI, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import os
from dotenv import load_dotenv

# Import our new modules
from config import config
from app_state import app_state

# Load environment variables (still needed for non-critical vars)
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="LangChain RAG API", version="1.0.0")

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# Initialize components on startup
@app.on_event("startup")
async def startup_event():
    await app_state.on_startup()

# ... rest of the endpoints, but now using app_state instead of globals
```

## Benefits of This Approach

1. **Centralized Configuration**: All configuration is in one place, making it easier to manage and update
2. **Type Safety**: Configuration values have proper types, reducing runtime errors
3. **Validation**: Required configuration values are validated at startup
4. **Dependency Injection**: Components are properly managed and injected where needed
5. **Testability**: Easier to mock configuration and dependencies in tests
6. **Maintainability**: Clear separation of concerns between configuration, state management, and business logic

## Next Steps

After implementing this phase, we'll move on to Phase 2: Database Refactoring, which will involve:
1. Creating SQLAlchemy models for our database tables
2. Implementing a proper database abstraction layer
3. Creating repository classes for data access