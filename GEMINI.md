## Project Overview

This project is a simple Retrieval Augmented Generation (RAG) application built with LangChain. It uses OpenRouter as the LLM provider and includes chat session persistence. The application is containerized using Docker and uses a PostgreSQL database to store chat history. It also features a simple web frontend.

The architecture consists of three main services:

*   **app:** A FastAPI application that provides the main API endpoints for chat, history, and document management.
*   **db:** A PostgreSQL database for storing chat history and session documents.
*   **ollama:** An Ollama service for generating embeddings.

## Building and Running

To build and run the project, you will need Docker and Docker Compose installed.

1.  **Set up the environment:**
    *   Navigate to the `app` directory.
    *   Copy the `.env.example` file to `.env`.
    *   Update the `.env` file with your OpenRouter API key.

2.  **Build and run the application:**
    *   From the `app` directory, run the following command:
        ```bash
        docker-compose up --build
        ```

3.  **Access the application:**
    *   Open your browser and navigate to `http://localhost:8000`.

## Development Conventions

*   The backend is written in Python using the FastAPI framework.
*   The frontend is a simple HTML/JavaScript application.
*   The application is designed to be run in a containerized environment using Docker.
*   Chat history and session documents are stored in a PostgreSQL database.
*   The RAG chain is implemented using LangChain and uses OpenRouter for the LLM and Ollama for embeddings.
