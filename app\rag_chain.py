from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.documents import Document
import os

# Import our new document processor
from document_processor import create_documents_from_texts, process_documents_for_rag, create_vectorstore_from_documents
from config import config

def format_docs(docs):
    """Format documents for context"""
    return "\n\n".join(doc.page_content for doc in docs)

def format_chat_history(chat_history):
    """Format chat history for the prompt"""
    formatted = ""
    for msg_type, content in chat_history:
        if msg_type == "user":
            formatted += f"Human: {content}\n"
        elif msg_type == "assistant":
            formatted += f"Assistant: {content}\n"
    return formatted.strip()

def create_rag_chain_from_wikipedia():
    """Create a RAG chain with OpenRouter integration using Wikipedia as source"""
    
    # Initialize OpenRouter LLM
    llm = ChatOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=config.OPENROUTER_API_KEY,
        model="z-ai/glm-4.5-air:free",
        temperature=0.7
    )
    
    # Create a simple document loader (in a real app, you'd load from various sources)
    # For this example, we'll load a sample document about AI
    loader = WebBaseLoader("https://en.wikipedia.org/wiki/Artificial_intelligence")
    documents = loader.load()
    
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200
    )
    splits = text_splitter.split_documents(documents)
    
    # Create embeddings and vector store
    embeddings = OllamaEmbeddings(
        model="nomic-embed-text",
        base_url=config.OLLAMA_BASE_URL
    )
    vectorstore = FAISS.from_documents(splits, embeddings)
    retriever = vectorstore.as_retriever()
    
    # Create prompt template that includes chat history
    template = """You are a helpful AI assistant. Answer the question based only on the following context:
{context}

Chat History:
{chat_history}

Question: {question}

Answer:"""
    
    prompt = ChatPromptTemplate.from_template(template)
    
    # Create the RAG chain
    rag_chain = (
        {
            "context": retriever | format_docs,
            "question": lambda x: x["question"],
            "chat_history": lambda x: format_chat_history(x["chat_history"])
        }
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return rag_chain

def create_rag_chain_from_session_documents(session_documents):
    """
    Create a RAG chain with OpenRouter integration using session-specific documents
    
    Args:
        session_documents: List of document dictionaries with 'content' and 'filename' keys
    
    Returns:
        RAG chain retriever
    """
    # Initialize OpenRouter LLM
    llm = ChatOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=config.OPENROUTER_API_KEY,
        model="z-ai/glm-4.5-air:free",
        temperature=0.7
    )
    
    # If no documents, return None or a default chain
    if not session_documents:
        return None
    
    # Create Document objects from session documents
    texts = [doc['content'] for doc in session_documents]
    filenames = [doc['filename'] for doc in session_documents]
    documents = create_documents_from_texts(texts, filenames)
    
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200
    )
    splits = text_splitter.split_documents(documents)
    
    # Create embeddings and vector store
    embeddings = OllamaEmbeddings(
        model="nomic-embed-text",
        base_url=config.OLLAMA_BASE_URL
    )
    vectorstore = FAISS.from_documents(splits, embeddings)
    retriever = vectorstore.as_retriever()
    
    # Create prompt template that includes chat history
    template = """You are a helpful AI assistant. Answer the question based only on the following context:
{context}

Chat History:
{chat_history}

Question: {question}

Answer:"""
    
    prompt = ChatPromptTemplate.from_template(template)
    
    # Create the RAG chain
    rag_chain = (
        {
            "context": retriever | format_docs,
            "question": lambda x: x["question"],
            "chat_history": lambda x: format_chat_history(x["chat_history"])
        }
        | prompt
        | llm
        | StrOutputParser()
    )
    
    return rag_chain