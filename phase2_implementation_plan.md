# Phase 2 Implementation Plan: Database Refactoring

## Overview
Phase 2 of the Mindflow codebase refactoring focuses on replacing the direct database access in `chat_history.py` with a proper database abstraction layer using SQLAlchemy ORM. This will address several key issues in the current implementation:

1. Direct use of psycopg2 with raw SQL queries
2. Lack of proper database abstraction
3. No ORM for data modeling
4. Inconsistent error handling
5. No connection pooling
6. No proper migration support

## Goals
1. Create SQLAlchemy models for the existing database tables
2. Implement a database abstraction layer with proper session management
3. Create repository classes for data access
4. Replace direct database access with the new abstraction
5. Maintain backward compatibility with existing functionality

## Implementation Steps

### Step 1: Create Database Models
- Create `models.py` with SQLAlchemy models for:
  - ChatHistory model
  - SessionDocuments model
- Define proper relationships and constraints
- Add data validation and serialization methods

### Step 2: Create Database Abstraction Layer
- Create `database.py` with:
  - Database connection management
  - Session handling with connection pooling
  - Migration support
  - Proper error handling

### Step 3: Create Repository Classes
- Create `repositories/` directory with:
  - `chat_history_repository.py`
  - `document_repository.py`
- Implement CRUD operations using the new database abstraction
- Add proper error handling and data validation

### Step 4: Update ChatHistoryManager
- Refactor `chat_history.py` to use the new repository classes
- Maintain the same public interface for backward compatibility
- Improve error handling

### Step 5: Testing
- Create unit tests for the new database models
- Create tests for repository classes
- Create integration tests for the updated ChatHistoryManager

## File Structure
```
app/
├── models.py                 # SQLAlchemy models
├── database.py               # Database abstraction layer
├── repositories/             # Repository classes
│   ├── __init__.py
│   ├── chat_history_repository.py
│   └── document_repository.py
└── chat_history.py           # Updated to use repositories
```

## Detailed Implementation

### 1. models.py
```python
from sqlalchemy import Column, Integer, String, Text, DateTime, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

Base = declarative_base()

class ChatHistory(Base):
    __tablename__ = 'chat_history'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String(255), nullable=False)
    message_type = Column(String(10), nullable=False)
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'message_type': self.message_type,
            'content': self.content,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class SessionDocuments(Base):
    __tablename__ = 'session_documents'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String(255), nullable=False)
    filename = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    upload_timestamp = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'session_id': self.session_id,
            'filename': self.filename,
            'content': self.content,
            'upload_timestamp': self.upload_timestamp.isoformat() if self.upload_timestamp else None
        }
```

### 2. database.py
```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from config import config
import logging

# Database URL construction
DATABASE_URL = f"postgresql://{config.POSTGRES_USER}:{config.POSTGRES_PASSWORD}@{config.DB_HOST}:{config.DB_PORT}/{config.POSTGRES_DB}"

# Create engine with connection pooling
engine = create_engine(
    DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    echo=False  # Set to True for SQL debugging
)

# Create scoped session factory
SessionLocal = scoped_session(sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
))

Base = declarative_base()

def get_db():
    """Dependency for FastAPI to get DB session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """Initialize database tables"""
    Base.metadata.create_all(bind=engine)
```

### 3. repositories/chat_history_repository.py
```python
from sqlalchemy.orm import Session
from typing import List, Tuple
from models import ChatHistory
from datetime import datetime

class ChatHistoryRepository:
    def __init__(self, db: Session):
        self.db = db
    
    def add_to_history(self, session_id: str, user_message: str, ai_response: str) -> bool:
        """Add user message and AI response to chat history"""
        try:
            # Add user message
            user_entry = ChatHistory(
                session_id=session_id,
                message_type='user',
                content=user_message
            )
            self.db.add(user_entry)
            
            # Add AI response
            ai_entry = ChatHistory(
                session_id=session_id,
                message_type='ai',
                content=ai_response
            )
            self.db.add(ai_entry)
            
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_history(self, session_id: str) -> List[Tuple[str, str]]:
        """Get chat history for a session as a list of (role, content) tuples"""
        try:
            entries = self.db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).order_by(ChatHistory.timestamp.asc()).all()
            
            # Convert to the format expected by our application
            history = []
            for entry in entries:
                if entry.message_type == 'user':
                    history.append(('user', entry.content))
                elif entry.message_type == 'ai':
                    history.append(('assistant', entry.content))
            
            return history
        except Exception as e:
            raise e
    
    def clear_history(self, session_id: str) -> int:
        """Clear chat history for a session"""
        try:
            count = self.db.query(ChatHistory).filter(
                ChatHistory.session_id == session_id
            ).delete()
            self.db.commit()
            return count
        except Exception as e:
            self.db.rollback()
            raise e
```

### 4. repositories/document_repository.py
```python
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from models import SessionDocuments
from datetime import datetime

class DocumentRepository:
    def __init__(self, db: Session):
        self.db = db
    
    def add_document(self, session_id: str, filename: str, content: str) -> int:
        """Add a document to a session"""
        try:
            document = SessionDocuments(
                session_id=session_id,
                filename=filename,
                content=content
            )
            self.db.add(document)
            self.db.commit()
            self.db.refresh(document)
            return document.id
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_documents(self, session_id: str) -> List[Dict[str, Any]]:
        """Get all documents for a session"""
        try:
            documents = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id
            ).order_by(SessionDocuments.upload_timestamp.asc()).all()
            
            return [doc.to_dict() for doc in documents]
        except Exception as e:
            raise e
    
    def delete_document(self, session_id: str, document_id: int) -> bool:
        """Delete a specific document from a session"""
        try:
            count = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id,
                SessionDocuments.id == document_id
            ).delete()
            self.db.commit()
            return count > 0
        except Exception as e:
            self.db.rollback()
            raise e
    
    def clear_documents(self, session_id: str) -> int:
        """Clear all documents for a session"""
        try:
            count = self.db.query(SessionDocuments).filter(
                SessionDocuments.session_id == session_id
            ).delete()
            self.db.commit()
            return count
        except Exception as e:
            self.db.rollback()
            raise e
```

## Benefits of This Approach

1. **ORM Benefits**: Type safety, relationship management, and easier querying
2. **Connection Pooling**: Better resource management and performance
3. **Proper Error Handling**: Transaction rollback on errors
4. **Repository Pattern**: Clear separation of data access logic
5. **Testability**: Easier to mock and test database interactions
6. **Maintainability**: Clearer code structure and better organization
7. **Migration Support**: Foundation for future database schema changes

## Next Steps After Implementation

1. Update `chat_history.py` to use the new repository classes
2. Create unit tests for all new components
3. Update the main application to use the new database abstraction
4. Test thoroughly to ensure backward compatibility
5. Document the new architecture