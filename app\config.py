import os
from typing import Optional

class Config:
    def __init__(self):
        # Load and validate OpenRouter configuration
        self.OPENROUTER_API_KEY: str = self._get_required_env_var("OPENROUTER_API_KEY")
        
        # Database configuration
        self.DB_HOST: str = os.getenv("DB_HOST", "db")
        self.POSTGRES_DB: str = os.getenv("POSTGRES_DB", "langchain_rag")
        self.POSTGRES_USER: str = os.getenv("POSTGRES_USER", "langchain")
        self.POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "langchain123")
        self.DB_PORT: str = os.getenv("DB_PORT", "5432")
        
        # Ollama configuration
        self.OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        
    def _get_required_env_var(self, var_name: str) -> str:
        value = os.getenv(var_name)
        if not value:
            raise ValueError(f"Required environment variable {var_name} is not set")
        return value

# Global config instance - only created when first accessed
_config_instance: Optional[Config] = None

def get_config() -> Config:
    global _config_instance
    if _config_instance is None:
        _config_instance = Config()
    return _config_instance

# For backward compatibility
config = get_config()